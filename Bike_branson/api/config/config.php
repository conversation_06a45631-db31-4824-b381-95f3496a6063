<?php
// CORS headers
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// Stripe configuration
define('STRIPE_SECRET_KEY', 'sk_test_51RpuXtQpePvVpMt30nDNC3EoJyXsvtNlchLC5d8opeKfQAbfg35ujoxqtz8ytV35kwTRqIwb1mnZy84ivc8MWaXG00R64hHrGz');
define('STRIPE_PUBLIC_KEY', 'pk_test_51RpuXtQpePvVpMt31ie4IWcw9nujspBNRbF3SKhFwYNOzDvH91irgf4R1Wv3vFHWdYsamyPyiBk4GQLkJLqb65st00ay33BB7');

// Email configuration
define('SMTP_HOST', 'smtp.hostinger.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_email_password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Bike Branson');

// Site configuration
define('SITE_URL', 'https://yourdomain.com');
define('ADMIN_EMAIL', '<EMAIL>');
?>